<!-- 聊天侧边栏组件 -->
<template>
  <div class="w-80 h-screen bg-white border-r border-gray-200 flex flex-col">
    <!-- 搜索栏 -->
    <div class="p-4 border-b border-gray-200">
      <div class="relative">
        <svg
          class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-base"
          viewBox="0 0 1024 1024"
          width="18px"
          height="18px"
        >
          <path
            d="M918.94 877.25L760.7 719.01c56.58-66.51 90.93-152.49 90.93-246.45 0-210.13-170.94-381.07-381.07-381.07S89.49 262.44 89.49 472.57s170.94 381.07 381.07 381.07c93.96 0 179.94-34.35 246.45-90.93l158.24 158.24c6.03 6.03 13.94 9.05 21.85 9.05s15.81-3.02 21.85-9.05c12.06-12.07 12.06-31.63-0.01-43.7zM151.29 472.57c0-176.05 143.22-319.28 319.28-319.28s319.28 143.22 319.28 319.28-143.23 319.27-319.29 319.27-319.27-143.22-319.27-319.27z"
            fill="#888888"
          ></path>
        </svg>
        <input
          type="text"
          class="w-full pl-9 pr-10 py-2 bg-gray-100 border-none rounded-md outline-none text-sm"
          placeholder="搜索"
          v-model="searchQuery"
          @input="handleSearch"
        />
        <button
          class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-transparent border-none cursor-pointer p-1 rounded text-gray-500 hover:bg-gray-100"
          @click="$emit('refresh')"
          title="刷新联系人列表"
        >
          <svg viewBox="0 0 1024 1024" width="20" height="20">
            <path
              d="M509.166933 230.4A281.6 281.6 0 1 0 793.6 512a280.302933 280.302933 0 0 0-65.672533-180.770133 25.6 25.6 0 0 1 39.253333-32.904534A331.502933 331.502933 0 0 1 844.8 512c0 183.808-148.992 332.8-332.8 332.8S179.2 695.808 179.2 512c0-177.493333 138.922667-322.491733 313.924267-332.288l-45.056-45.056a25.6 25.6 0 1 1 36.181333-36.215467l96.5632 96.529067a25.6 25.6 0 0 1 0 36.215467l-96.5632 96.529066a25.6 25.6 0 1 1-36.181333-36.181333l61.098666-61.098667z"
              fill="#979797"
            ></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- 联系人列表 -->
    <div class="flex-1 overflow-y-auto">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex flex-col items-center justify-center p-8">
        <div class="text-gray-500 text-sm mb-2">{{ loadingText || '加载联系人中...' }}</div>
        <div
          class="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"
        ></div>
      </div>

      <!-- 联系人列表 -->
      <div v-else>
        <ContactItem
          v-for="contact in filteredContacts"
          :key="contact.id"
          :contact="contact"
          :is-active="currentContactId === contact.id"
          @select="$emit('select-contact', $event)"
        />

        <!-- 空状态 -->
        <div
          v-if="!filteredContacts.length && !isLoading"
          class="flex items-center justify-center p-8"
        >
          <div class="text-gray-500 text-sm">暂无联系人</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ContactItem from './ContactItem.vue'
import type { User } from '../api'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
}

interface Props {
  contacts: Contact[]
  currentContactId: string | null
  isLoading: boolean
  loadingText?: string
}

const props = defineProps<Props>()

defineEmits<{
  'select-contact': [id: string]
  refresh: []
}>()

// 搜索功能
const searchQuery = ref('')

const filteredContacts = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.contacts
  }
  return props.contacts.filter((contact) =>
    contact.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
}
</script>
