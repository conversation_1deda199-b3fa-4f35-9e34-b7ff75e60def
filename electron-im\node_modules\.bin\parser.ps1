#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@babel+parser@7.28.3\node_modules\@babel\parser\bin\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@babel+parser@7.28.3\node_modules\@babel\parser\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@babel+parser@7.28.3\node_modules\@babel\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@babel+parser@7.28.3\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@babel+parser@7.28.3/node_modules/@babel/parser/bin/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@babel+parser@7.28.3/node_modules/@babel/parser/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@babel+parser@7.28.3/node_modules/@babel/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@babel+parser@7.28.3/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../@babel/parser/bin/babel-parser.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../@babel/parser/bin/babel-parser.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../@babel/parser/bin/babel-parser.js" $args
  } else {
    & "node$exe"  "$basedir/../@babel/parser/bin/babel-parser.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
