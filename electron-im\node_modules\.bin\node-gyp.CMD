@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node-gyp@9.4.1\node_modules\node-gyp\bin\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node-gyp@9.4.1\node_modules\node-gyp\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node-gyp@9.4.1\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node-gyp@9.4.1\node_modules\node-gyp\bin\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node-gyp@9.4.1\node_modules\node-gyp\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node-gyp@9.4.1\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\node-gyp\bin\node-gyp.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\node-gyp\bin\node-gyp.js" %*
)
