#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-vite@4.0.0_vite@7._0a1a547f46ce01d737f5627414e55cda/node_modules/electron-vite/bin/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-vite@4.0.0_vite@7._0a1a547f46ce01d737f5627414e55cda/node_modules/electron-vite/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-vite@4.0.0_vite@7._0a1a547f46ce01d737f5627414e55cda/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-vite@4.0.0_vite@7._0a1a547f46ce01d737f5627414e55cda/node_modules/electron-vite/bin/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-vite@4.0.0_vite@7._0a1a547f46ce01d737f5627414e55cda/node_modules/electron-vite/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-vite@4.0.0_vite@7._0a1a547f46ce01d737f5627414e55cda/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../electron-vite/bin/electron-vite.js" "$@"
else
  exec node  "$basedir/../electron-vite/bin/electron-vite.js" "$@"
fi
