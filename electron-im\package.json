{"name": "electron-im", "version": "1.0.0", "description": "An Electron application with Vue and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "pnpm run typecheck:node && pnpm run typecheck:web", "start:web": "vite --config vite.config.mjs", "start:electron": "electron-vite dev", "build:web": "pnpm run typecheck:web && vite build", "electron:build:mac": "pnpm run typecheck:node && electron-vite build && electron-builder --mac", "electron:build:win": "pnpm run typecheck:node && electron-vite build && electron-builder --win", "electron:build:linux": "pnpm run typecheck:node && electron-vite build && electron-builder --linux", "build": "pnpm run build:web && pnpm run electron:build:mac && pnpm run electron:build:win && pnpm run electron:build:linux", "postinstall": "electron-builder install-app-deps", "prepare": "cd .. && husky . electron-im/.husky"}, "dependencies": {"@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@types/protobufjs": "^6.0.0", "ant-design-vue": "^4.2.6", "pinia": "^2.2.8", "protobufjs": "^7.5.4", "tailwindcss": "^4.1.12"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@tailwindcss/vite": "^4.1.12", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.0", "cross-env": "^10.0.0", "electron": "^37.2.3", "electron-builder": "^25.1.8", "electron-vite": "^4.0.0", "eslint": "^9.31.0", "eslint-plugin-vue": "^10.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "typescript": "^5.8.3", "vite": "^7.0.5", "vue": "^3.5.17", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^3.0.3"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild"]}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix", "prettier --write"]}}