@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@electron+osx-sign@1.3.1\node_modules\@electron\osx-sign\bin\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@electron+osx-sign@1.3.1\node_modules\@electron\osx-sign\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@electron+osx-sign@1.3.1\node_modules\@electron\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@electron+osx-sign@1.3.1\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@electron+osx-sign@1.3.1\node_modules\@electron\osx-sign\bin\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@electron+osx-sign@1.3.1\node_modules\@electron\osx-sign\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@electron+osx-sign@1.3.1\node_modules\@electron\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\@electron+osx-sign@1.3.1\node_modules;D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@electron\osx-sign\bin\electron-osx-sign.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@electron\osx-sign\bin\electron-osx-sign.js" %*
)
