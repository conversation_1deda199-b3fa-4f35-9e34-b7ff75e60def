#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/vite@7.1.2_@types+node@22.1_789da8ac69f78c0e37cc2fe9ef0d4fb0/node_modules/vite/bin/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/vite@7.1.2_@types+node@22.1_789da8ac69f78c0e37cc2fe9ef0d4fb0/node_modules/vite/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/vite@7.1.2_@types+node@22.1_789da8ac69f78c0e37cc2fe9ef0d4fb0/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/vite@7.1.2_@types+node@22.1_789da8ac69f78c0e37cc2fe9ef0d4fb0/node_modules/vite/bin/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/vite@7.1.2_@types+node@22.1_789da8ac69f78c0e37cc2fe9ef0d4fb0/node_modules/vite/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/vite@7.1.2_@types+node@22.1_789da8ac69f78c0e37cc2fe9ef0d4fb0/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi
