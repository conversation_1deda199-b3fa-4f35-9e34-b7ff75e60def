#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-builder@25.1.8_ele_51d3854c946349715af5c2898679b81d/node_modules/electron-builder/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-builder@25.1.8_ele_51d3854c946349715af5c2898679b81d/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-builder@25.1.8_ele_51d3854c946349715af5c2898679b81d/node_modules/electron-builder/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/electron-builder@25.1.8_ele_51d3854c946349715af5c2898679b81d/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../electron-builder/install-app-deps.js" "$@"
else
  exec node  "$basedir/../electron-builder/install-app-deps.js" "$@"
fi
