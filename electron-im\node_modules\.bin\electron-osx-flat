#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@electron+osx-sign@1.3.1/node_modules/@electron/osx-sign/bin/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@electron+osx-sign@1.3.1/node_modules/@electron/osx-sign/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@electron+osx-sign@1.3.1/node_modules/@electron/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@electron+osx-sign@1.3.1/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@electron+osx-sign@1.3.1/node_modules/@electron/osx-sign/bin/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@electron+osx-sign@1.3.1/node_modules/@electron/osx-sign/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@electron+osx-sign@1.3.1/node_modules/@electron/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/@electron+osx-sign@1.3.1/node_modules:/mnt/d/Code/zhuyuqian1/electron-im/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@electron/osx-sign/bin/electron-osx-flat.js" "$@"
else
  exec node  "$basedir/../@electron/osx-sign/bin/electron-osx-flat.js" "$@"
fi
